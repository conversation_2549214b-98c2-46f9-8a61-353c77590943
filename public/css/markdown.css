/**
 * Markdown渲染样式表 - markdown.css
 *
 * 本文件定义了Markdown内容渲染的所有样式，包括：
 * 1. 预览容器：整体布局、滚动和交互行为
 * 2. 文本元素：标题、段落、列表等基本Markdown元素样式
 * 3. 代码块：行内代码和代码块的高亮样式
 * 4. 特殊元素：引用、表格、链接等特殊Markdown元素样式
 * 5. 编辑提示：双击编辑等用户引导元素
 *
 * 这些样式确保了Markdown内容的美观展示和良好的阅读体验，
 * 同时提供了编辑模式和预览模式的无缝切换。
 */

/* Markdown 渲染样式 */

/* Markdown 渲染容器样式 */
.markdown-preview {
  width: 100%;
  height: 100%;
  padding: 10px 12px;
  overflow-y: auto;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.8);
  background-color: transparent;
  display: none; /* 默认隐藏预览 */
  scrollbar-width: none; /* Firefox */
  cursor: text; /* 使用文本光标提示可编辑 */
  position: relative;
  z-index: 1; /* 较低的层级，确保编辑提示在上层 */
}

/* 移除原来的 :after 伪元素样式 */
.markdown-preview:after {
  display: none; /* 完全移除旧的提示元素 */
}

.note:hover .markdown-preview:after {
  display: none;
}

/* 添加独立的编辑提示按钮样式 */
.edit-hint {
  position: absolute !important;
  right: 10px !important;
  bottom: 10px !important;
  font-size: 11px !important;
  color: rgba(0, 0, 0, 0.5) !important;
  background-color: rgba(255, 255, 255, 0.7) !important;
  padding: 3px 8px !important;
  border-radius: 4px !important;
  opacity: 0; /* 默认隐藏，由JavaScript控制显示 */
  transition: opacity 0.3s, background-color 0.2s !important;
  pointer-events: none !important; /* 默认不干扰其他交互，由JavaScript控制 */
  z-index: 100 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  user-select: none !important;
  border: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* 修改悬停规则，只在非编辑模式下显示提示
 * 注意：实际显示逻辑由JavaScript控制，这里不再使用!important强制覆盖
 */
/* 移除这个规则，完全由JavaScript控制提示的显示和隐藏 */

/* Markdown 格式化样式 */
.markdown-preview h1,
.markdown-preview h2,
.markdown-preview h3 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: 600;
}

.markdown-preview h1 {
  font-size: 20px;
}

.markdown-preview h2 {
  font-size: 18px;
}

.markdown-preview h3 {
  font-size: 16px;
}

.markdown-preview p {
  margin-bottom: 10px;
}

/* 简洁的分割线样式 */
.markdown-preview hr {
  height: 1px;
  border: none;
  background-color: rgba(0, 0, 0, 0.1);
  margin: 15px 0;
}

.markdown-preview ul,
.markdown-preview ol {
  margin-left: 20px;
  margin-bottom: 10px;
}

.markdown-preview code {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
}

.markdown-preview pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  margin-bottom: 10px;
}

.markdown-preview pre code {
  background-color: transparent;
  padding: 0;
}

.markdown-preview blockquote {
  border-left: 4px solid rgba(0, 0, 0, 0.1);
  padding-left: 10px;
  margin-left: 0;
  color: rgba(0, 0, 0, 0.6);
}

.markdown-preview a {
  color: #1a73e8;
  text-decoration: none;
}

.markdown-preview a:hover {
  text-decoration: underline;
}

.markdown-preview img {
  max-width: 100%;
}

.markdown-preview table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 10px;
}

.markdown-preview table,
.markdown-preview th,
.markdown-preview td {
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.markdown-preview th,
.markdown-preview td {
  padding: 5px;
  text-align: left;
}

/* 添加便签模式切换按钮样式 */
.note-toggle-button {
  display: none; /* 隐藏切换按钮 */
}

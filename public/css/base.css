/**
 * 基础样式表 - base.css
 * 
 * 本文件定义了整个应用的基础样式和重置规则，包括：
 * 1. 样式重置：清除浏览器默认样式，确保跨浏览器的一致性
 * 2. 页面布局：设置基本的布局结构，如容器样式
 * 3. 共用元素：定义常用元素（如按钮）的基本样式
 * 
 * 这些样式为应用提供统一的基础外观和布局结构，
 * 是其他组件样式的基础。
 */

/* 基础样式和重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background-color: #f5f5f5;
  height: 100vh;
  overflow: hidden;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
  background-color: #fcfcfc;
}

/* 按钮基本样式 */
button {
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  /* 移除默认内边距，让特定按钮自己定义自己的内边距 */
  padding: 0;
  /* 添加盒模型设置，确保边框不会改变按钮尺寸 */
  box-sizing: border-box;
}
